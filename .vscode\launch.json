{
    "version": "0.2.0",
    "configurations": [
        // {
        //     "name": "Python: Current File",
        //     "type": "python",
        //     "request": "launch",
        //     "program": "${file}",
        //     "args": [],
        //     "env": {
        //         "PYDEVD_DISABLE_FILE_VALIDATION": "1"
        //     },
        //     "args": ["-Xfrozen_modules=off"]
        // },
        {
            "name": "Python: Excel Comparison (Single File)",
            "type": "python",
            "request": "launch",
            "program": "${workspaceFolder}/run_all_tasks.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "cwd": "${workspaceFolder}",
            "args": [
                "${workspaceFolder}/input/processing_invoices.xlsx",
                "${workspaceFolder}/input/processing_checklist.xlsx",
                "${workspaceFolder}/input/duty_rate.xlsx",
                "${workspaceFolder}/output/processed_invoices.xlsx",
                "${workspaceFolder}/output/processed_checklist.xlsx",
                "${workspaceFolder}/output/processed_report.xlsx"
            ],
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            }
        },
        // {
        //     "name": "Python: Process Multiple Invoice Files",
        //     "type": "python",
        //     "request": "launch",
        //     "program": "${workspaceFolder}/processing_invoices.py",
        //     "console": "integratedTerminal",
        //     "justMyCode": true,
        //     "cwd": "${workspaceFolder}",
        //     "args": [],
        //     "env": {
        //         "PYTHONPATH": "${workspaceFolder}"
        //     }
        // },
        // {
        //     "name": "Python: Excel Comparison",
        //     "type": "python",
        //     "request": "launch",
        //     "program": "${workspaceFolder}/processing_report.py",
        //     "console": "integratedTerminal",
        //     "justMyCode": true,
        //     "cwd": "${workspaceFolder}",
        //     "args": ["output/processed_invoices.xlsx", "output/processed_checklist.xlsx", "output/processed_report.xlsx"],
        //     "env": {
        //         "PYTHONPATH": "${workspaceFolder}"
        //     }
        // },


        //     {
        //         "name": "Python: Excel processing_checklist",
        //         "type": "python",
        //         "request": "launch",
        //         "program": "${workspaceFolder}/processing_checklist.py",
        //         "console": "integratedTerminal",
        //         "justMyCode": true,
        //         "cwd": "${workspaceFolder}",
        //         "args": [],
        //         "env": {
        //             "PYTHONPATH": "${workspaceFolder}"
        //         }
        // },



        // {
        //     "name": "Python: Excel processing_invoices",
        //     "type": "python",
        //     "request": "launch",
        //     "program": "${workspaceFolder}/processing_invoices.py",
        //     "console": "integratedTerminal",
        //     "justMyCode": true,
        //     "cwd": "${workspaceFolder}",
        //     "args": [],
        //     "env": {
        //         "PYTHONPATH": "${workspaceFolder}"
        //     }
        // }
    ]
}