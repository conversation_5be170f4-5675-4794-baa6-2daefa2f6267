# Excel 数据处理系统文档

## 1. processing_invoices.py 进口发票处理器
### 输入文件说明
- **进口发票文件 (processing_invoices.xlsx)**
  - 📂 文件要求：
    - 必须包含多个工作表（Sheet）
    - 工作表名称应以 CI- 开头（如：CI-2023-INV）
    - 每个工作表包含以下关键列：

  | 列名         | 作用/目的                          | 数据示例           | 注意事项                 |
  |-------------|-----------------------------------|-------------------|------------------------|
  | Item#       | 商品项次编号                       | 1, 2, 3...        | 必须为连续数字            |
  | P/N         | 部件编号/发票号                    | INV-2023-001      | 包含字母数字组合          |
  | Desc        | 商品详细描述                       | "轴承-型号X-不锈钢" | 应包含完整规格信息        |
  | Qty         | 购买数量                           | 50                | 必须为整数               |
  | Price       | 单价（美元）                       | 12.50             | 保留两位小数             |

- **关税主税率表 (duty_rate.xlsx)**
  | 列名         | 作用/目的                          | 数据示例           | 注意事项                 |
  |-------------|-----------------------------------|-------------------|------------------------|
  | Item Name   | 标准化商品名称                     | "深沟球轴承"       | 应与Desc开头部分匹配      |
  | HSN1/HSN2   | 海关编码                          | 8482.10.00        | 8-10位数字代码           |
  | Final BCD   | 基础关税税率(%)                   | 7.5               | 百分比数值               |
  | Final SWS   | 社会福利附加税(%)                 | 0.05              | 百分比数值               |
  | Final IGST  | 综合商品服务税(%)                 | 12.0              | 百分比数值               |

### 输出文件说明
- **处理后的检查清单 (processed_checklist.xlsx)**
  | 列名         | 包含内容                          | 作用说明                 |
  |-------------|----------------------------------|-------------------------|
  | ID          | 发票号_项次号 (如: 2023INV_1)    | 唯一标识每个商品项        |
  | Item_Name   | 简化的商品名称 (如: "轴承")       | 用于快速识别商品类型       |
  | HSN         | 匹配的海关编码                    | 清关申报使用              |
  | Duty        | 计算后的关税金额                  | 基于税率自动计算           |

- **新增税率记录表 (added_new_items.xlsx)**
  ▶️ 当发现未登记商品时自动生成
  | 列名         | 作用说明                          |
  |-------------|----------------------------------|
  | 发票及项号    | 发现新商品的来源位置               |
  | Item Name   | 系统识别到的新商品名称             |

## 2. processed_checklist.py 数据清洗工具
### 输入文件
- **历史核对表原始数据 (processing_checklist.xlsx)**
  | 列名         | 常见问题                          | 清洗规则                 |
  |-------------|----------------------------------|-------------------------|
  | Desc        | 包含特殊符号/多余信息             | 自动移除"+OR-"等非标准字符 |
  | HSN         | 可能包含错误/过时编码             | 根据标准数据库验证         |
  | Price       | 不同货币单位混合                  | 统一转换为人民币金额       |

### 输出文件
- **标准化核对表 (processed_checklist.xlsx)**
  ✅ 标准化后的数据包含：
  - 统一格式的商品描述
  - 验证通过的海关编码
  - 转换完成的本地货币价格

## 3. processing_report.py 差异报告生成器
### 输入文件要求
1. **处理后的检查清单** (来自create_check.py)
2. **标准化核对表** (来自clean_check.py)

### 输出报告示例
| ID          | 差异字段       | 变更内容              |
|-------------|---------------|----------------------|
| 2023INV_1   | Price         | 150.00 → 165.00     |
| 2023INV_5   | HSN           | 8482.10 → 8482.20   |

## 工作流程图示
📥 原始发票 → processing_invoices.py → 📄 处理后的检查清单
🔍 现有数据 → clean_check.py → ✨ 清洁数据
🔄 processing_report.py 对比 → ❗ 核对差异报告

## 常见问题解答
Q: 如何准备进口发票文件？
A: 1. 每个发票单独一个工作表
   2. 工作表命名格式：CI-发票号
   3. 保留原始数据格式不要修改

Q: 发现"new item"提示怎么办？
A: 1. 检查added_new_items.xlsx
   2. 补充缺失的税率信息
   3. 重新运行程序即可 