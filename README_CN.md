# checklist检查工具

## 1. 流程概述 🌟

### 1.1 数据验证工作流的关键阶段

1. **🌱 开始** - 启动验证工作流
2. **📥 加载文件** - 导入三个关键文档：
   - 海关CheckList文档(可能有差异)
   - 进口发票（精准数据）
   - 关税率汇总文件(可能货描不全)

3. **🔄 标准化检查** - 确定是否需要数据标准化：
   - **🔧 标准化海关CheckList文档**（如需要）
   - **📦 标准化进口发票**（如需要）

4. **✅ 核心验证** - 交叉引用数据：
  - 三方数据对比：
    1. 海关CheckList文档（主数据）包含字段：Item#, ID(invoice#_item#),  P/N, Desc, Qty, Price,Item_Name, HSN, BCD, SWS, IGST
    2. 进口发票数据 Item#, ID(invoice#_item#),  P/N, Desc, Qty, Price,Item_Name, HSN, BCD, SWS, IGST
    3. 关税率汇总数据 Item Name,Final BCD,Final SWS,Final IGST
    
    
  - 验证步骤：
    a) **编号匹配**：
       - **主要匹配**：发票号(Invoice No.)和Item#必须完全一致
       - **次要匹配**：P/N只需大致相同（允许存在小的差异，如大小写、特殊字符等）即判定相同
       - **P/N截断处理**：
         * 对于存在包含关系的P/N（如 1.2.08.0 vs 1.2.08.09.10084）：
         * 系统不同的P/N需人工确认(暂不检测前缀匹配或包含关),在最终报道展示,需人工判断
    b) **描述检查**：
       - 比对前需统一特殊字符格式
       - 允许描述存在等效表达（如温度符号"°"可以是"度"、"DEG"等）
       - 描述大致相同即可判定为同一物料
       - 对于细微差异的描述（如 CAPACITOR0.1UF1016VX5R(5585C)0402 vs CAPACITOR0.1UF1016VX5R(5585)0402"需人工确认",在最终报道展示,需人工判断
    c) **数量验证**：检查海关CheckList的Qty需与发票的Quantity PCS一致
    d) **价格容差**：允许±1.1%(经验数值)的单位价格差异
    e) **税率核查**：确保HSN编码匹配关税率汇总文件并验证：
       - HSN编码以纯数字形式表示，无需小数点（如：85045090 -> 85051110）
       - BCD vs 最终BCD
       - SWS vs 最终SWS
       - IGST vs 最终IGST
    f) **新增Item Name处理**：
       - 未识别物料添加至关税率汇总文件底部并标记为'新增条目'
       - 税率显示格式：原税率 -> 新税率项目（如：15.0 -> 新税率项目）
       - HSN编码使用纯数字格式，不带小数点
       - 此标记便于后续人工确认和更新正确税率

5. **❓ 验证结果** - 评估结果：
   - **✔ 成功** → 生成合规报告
   - **❌ 发现错误** → 记录问题 → 生成错误报告

6. **🏁 完成** - 完成验证过程

### 1.2 基础流程摘要
```mermaid
flowchart LR
    A[开始] --> B[加载文件] --> C{需要标准化?}
    C -->|是| D[标准化海关CheckList] & E[标准化进口发票]
    C -->|否| F[验证]
    D & E --> F --> G{有效?}
    G -->|是| H[报告]
    G -->|否| I[记录错误] --> H
    H --> J[结束]
```

### 1.3 详细流程说明

#### 1.3.1 标准化流程
```mermaid
flowchart TD
    A[开始标准化] --> B[检查文件格式]
    B --> C{Excel格式有效?}
    C -->|否| D[记录格式错误]
    C -->|是| E[读取Excel内容]
    
    E --> F[清理列名]
    F --> G[移除特殊字符]
    F --> H[统一大小写]
    F --> I[移除空白]
    
    E --> J[处理数据行]
    J --> K[移除空行]
    J --> L[清理单元格值]
    J --> M[转换数据类型]
    
    K & L & M --> N[验证必填字段]
    G & H & I --> N
    
    N --> O{所有字段有效?}
    O -->|否| P[记录字段错误]
    O -->|是| Q[保存标准化文件]
    
    P --> R[错误结束]
    Q --> S[成功完成]
```

#### 1.3.2 验证流程
```mermaid
flowchart TD
    A[开始验证] --> B[加载标准化文件]
    B --> C[三方数据对比]
    C --> D[海关CheckList文档]
    C --> E[进口发票]
    C --> F[税率清单表]
    
    D --> G[编号匹配]
    E --> G
    G --> H1{发票号和Item#完全匹配?}
    H1 -->|否| I[记录异常：编号不匹配]
    H1 -->|是| H2{P/N比对}
    H2 -->|完全不同| I2[记录警告：P/N差异较大]
    H2 -->|存在包含关系| I3[标记：P/N需人工确认]
    H2 -->|相同或等效| J[描述标准化]
    
    I3 --> J
    J --> K[移除特殊字符]
    J --> L[统一大小写]
    J --> L2[统一等效表达]
    K & L & L2 --> M[比对描述]
    M --> M2{描述差异程度?}
    M2 -->|完全不同| M3[记录警告：描述差异较大]
    M2 -->|细微差异| M4[标记为需人工确认]
    M2 -->|相同或等效| N[数量验证]
    
    G --> O[价格验证]
    O --> P{±1.1% 容差?}
    P -->|否| Q[记录价格偏差]
    
    G --> R[新材料检查]
    R --> S{已识别?}
    S -->|否| T[注册到税率表]
    T --> U[标记为'新增']
    
    N & P & S -->|全部有效| V[生成合规报告]
    I & I2 & Q --> W[编制错误报告]
    
    G --> X[检查数量]
    G --> Y[检查价格]
    G --> Z[检查描述]
    
    X --> AA{数量有效?}
    Y --> AB{价格有效?}
    Z --> AC{描述匹配?}
    
    AA -->|否| AD[记录数量错误]
    AB -->|否| AE[记录价格错误]
    AC -->|否| AF[记录描述错误]
    
    AA & AB & AC -->|是| AG[检查关税率]
    AG --> AH{关税率有效?}
    AH -->|否| AI[记录关税错误]
    AH -->|是| AJ[验证通过]
    
    AD & AE & AF & AI --> AK[编制错误报告]
    AJ --> AK[生成成功报告]
    
    AK --> AL[结束验证]
```

> **注意：** 这个精简的流程确保了数据一致性和合规性，同时通过全面的报告维护清晰的审计跟踪。
